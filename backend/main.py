from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import redis.asyncio as redis
import uuid, json, httpx, os
import asyncio
import websockets
import base64

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
redis_client = redis.Redis(host=os.environ.get("REDIS_HOST", "localhost"), port=int(os.environ.get("REDIS_PORT", "6379")), decode_responses=True)
SPEECHMATICS_API_KEY = os.environ.get("SPEECHMATICS_API_KEY")
SM_WS_URL = os.environ.get("SM_WS_URL")
LANG = "cmn_en"
SAMPLE_RATE = 16000
print(f"[SPEECHMATICS_API_KEY] {SPEECHMATICS_API_KEY} URL: {SM_WS_URL}")

# ElevenLabs configuration
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")
ELEVENLABS_VOICE_ID = os.getenv("ELEVENLABS_VOICE_ID", "21m00Tcm4TlvDq8ikWAM")  # Default voice
ELEVENLABS_WS_URL = "wss://api.elevenlabs.io/v1/text-to-speech/{voice_id}/stream-input?model_id=eleven_turbo_v2"

# Session-specific voice cache to ensure consistency
session_voice_cache = {}  # {session_code: voice_id}

def session_key(code): return f"session:{code}"

audience_websockets = {}  # {session_code: set()}
speaker_websockets = {}  # {session_code: WebSocket}

# Pydantic models
class SessionCreateRequest(BaseModel):
    input_language: str = "en"
    output_language: str = "zh"

class JoinSessionRequest(BaseModel):
    audience_id: str

# Helper: Notify speaker and audience of new audience count
async def broadcast_audience_count(code):
    key = f"{session_key(code)}:audience"
    count = await redis_client.scard(key)
    print(f"[DEBUG] Broadcasting audience count for session {code}: {count}")

    # Notify audience members
    ws = audience_websockets.get(code, set())
    for w in ws:
        try:
            await w.send_json({"audience_count": count})
        except Exception as e:
            print(f"[DEBUG] Error sending audience count to audience member: {e}")

    # Notify speaker if connected
    speaker_ws = speaker_websockets.get(code)
    if speaker_ws:
        try:
            await speaker_ws.send_json({"audience_count": count})
            print(f"[DEBUG] Sent audience count {count} to speaker for session {code}")
        except Exception as e:
            print(f"[DEBUG] Error sending audience count to speaker: {e}")
            # Remove invalid speaker websocket
            speaker_websockets.pop(code, None)


@app.post("/session/create")
async def create_session(session_data: SessionCreateRequest = None):
    """Create a new translation session with metadata."""
    code = str(uuid.uuid4())[:8].upper()

    # Default session metadata
    metadata = {
        "status": "active",
        "created_at": str(int(asyncio.get_event_loop().time())),
        "input_language": session_data.input_language if session_data else "en",
        "output_language": session_data.output_language if session_data else "zh",
        "speaker_connected": "false",
        "total_segments": "0"
    }

    # Store session metadata
    await redis_client.hset(session_key(code), mapping=metadata)
    print(f"[REDIS] Created session {code} with metadata: {metadata}")

    # Initialize audience set (without dummy data)
    await redis_client.delete(f"{session_key(code)}:audience")
    print(f"[REDIS] Initialized audience set for session {code}")

    # Initialize WebSocket tracking
    audience_websockets[code] = set()

    return {
        "code": code,
        "session": metadata
    }




@app.post("/session/join/{code}")
async def join_session(code: str, request_data: JoinSessionRequest):
    """Join an existing session as audience member."""
    code = code.upper()

    # Check if session exists
    session_exists = await redis_client.exists(session_key(code))
    print(f"[REDIS] Checking if session {code} exists: {session_exists}")
    if not session_exists:
        return {"error": "Session not found", "joined": False}, 404

    # Check session status
    session_status = await redis_client.hget(session_key(code), "status")
    print(f"[REDIS] Session {code} status: {session_status}")
    if session_status != "active":
        return {"error": "Session is not active", "joined": False}, 400

    audience_id = request_data.audience_id or f"audience_{int(asyncio.get_event_loop().time())}"

    # Add audience member
    await redis_client.sadd(f"{session_key(code)}:audience", audience_id)
    print(f"[REDIS] Added audience member {audience_id} to session {code}")
    await broadcast_audience_count(code)

    # Get session info
    session_info = await redis_client.hgetall(session_key(code))
    print(f"[REDIS] Retrieved session info for {code}: {session_info}")

    return {
        "joined": True,
        "session": session_info,
        "audience_id": audience_id
    }


@app.websocket("/ws/audience/{code}")
async def ws_audience(websocket: WebSocket, code: str):
    await websocket.accept()
    audience_websockets.setdefault(code, set()).add(websocket)
    await broadcast_audience_count(code)
    pubsub = redis_client.pubsub()
    await pubsub.subscribe(f"{session_key(code)}:events")
    try:
        while True:
            msg = await pubsub.get_message(ignore_subscribe_messages=True, timeout=10)
            if msg:
                await websocket.send_text(msg["data"])
    except WebSocketDisconnect:
        audience_websockets[code].discard(websocket)
        await broadcast_audience_count(code)
        await pubsub.unsubscribe(f"{session_key(code)}:events")


@app.websocket("/ws/speaker/{code}")
async def ws_speaker(websocket: WebSocket, code: str):
    code = code.upper()
    await websocket.accept()

    # Check if session exists
    session_exists = await redis_client.exists(session_key(code))
    print(f"[DEBUG] Session {code} exists: {session_exists}")
    if not session_exists:
        await websocket.send_text(json.dumps({
            "message": "Error",
            "error": "Session not found"
        }))
        await websocket.close()
        return

    # Check if speaker is already connected (single speaker restriction)
    current_speaker_status = await redis_client.hget(session_key(code), "speaker_connected")
    print(f"[DEBUG] Current speaker_connected status for session {code}: {current_speaker_status}")

    if current_speaker_status == "true" and code in speaker_websockets:
        print(f"[DEBUG] Speaker already connected to session {code}, rejecting new connection")
        await websocket.send_text(json.dumps({
            "message": "Error",
            "error": "Speaker already connected to this session"
        }))
        await websocket.close()
        return

    # Mark speaker as connected and store WebSocket reference
    await redis_client.hset(session_key(code), "speaker_connected", "true")
    speaker_websockets[code] = websocket
    print(f"[DEBUG] Speaker connected to session {code}, speaker_connected set to true")

    # Small delay to ensure Redis write is committed before proceeding
    await asyncio.sleep(0.1)

    # Broadcast current audience count to the newly connected speaker
    await broadcast_audience_count(code)

    audio_buffer = bytearray()
    session_id = f"{code}_{id(websocket)}"

    # Get existing segments for context (for rejoining)
    existing_segments = []
    try:
        segments_data = await redis_client.lrange(f"{session_key(code)}:segments", 0, -1)
        for segment_json in segments_data:
            try:
                segment = json.loads(segment_json)
                existing_segments.append(segment)
            except json.JSONDecodeError:
                continue
    except Exception as e:
        print(f"[DEBUG] Error loading existing segments: {e}")

    try:
        await handle_live_mode(websocket, session_id, audio_buffer, existing_segments, code)
    except Exception as e:
        print(f"[ERROR] [{session_id}] Speaker WebSocket error: {e}")
    finally:
        # Mark speaker as disconnected when WebSocket closes
        await redis_client.hset(session_key(code), "speaker_connected", "false")
        # Remove speaker WebSocket reference
        speaker_websockets.pop(code, None)
        print(f"[DEBUG] [{session_id}] Speaker disconnected from session {code}, speaker_connected set to false")

        # Notify audience about speaker disconnection
        disconnect_message = json.dumps({
            "message": "SpeakerDisconnected",
            "timestamp": int(asyncio.get_event_loop().time())
        })
        await redis_client.publish(f"{session_key(code)}:events", disconnect_message)


@app.get("/session/state/{code}")
async def session_state(code: str):
    """Get current session state and statistics."""
    code = code.upper()

    # Check if session exists
    session_exists = await redis_client.exists(session_key(code))
    print(f"[REDIS] Session state check - session {code} exists: {session_exists}")
    if not session_exists:
        return {"error": "Session not found"}, 404

    # Get session data
    data = await redis_client.hgetall(session_key(code))
    audience_count = await redis_client.scard(f"{session_key(code)}:audience")
    print(f"[REDIS] Session {code} data: {data}")
    print(f"[REDIS] Session {code} audience count: {audience_count}")

    # Get transcript segments count
    segments_count = await redis_client.llen(f"{session_key(code)}:segments")
    print(f"[REDIS] Session {code} segments count: {segments_count}")

    return {
        "code": code,
        "audience_count": audience_count,
        "segments_count": segments_count,
        **data
    }

@app.get("/session/validate/{code}")
async def validate_session(code: str):
    """Validate if a session exists and is active."""
    code = code.upper()

    session_exists = await redis_client.exists(session_key(code))
    print(f"[REDIS] Validate session - session {code} exists: {session_exists}")
    if not session_exists:
        return {"valid": False, "error": "Session not found"}

    session_status = await redis_client.hget(session_key(code), "status")
    print(f"[REDIS] Validate session - session {code} status: {session_status}")
    if session_status != "active":
        return {"valid": False, "error": "Session is not active"}

    session_info = await redis_client.hgetall(session_key(code))
    print(f"[REDIS] Validate session - session {code} info: {session_info}")
    return {
        "valid": True,
        "session": session_info
    }

@app.post("/session/speaker/connect/{code}")
async def speaker_connect(code: str):
    """Mark speaker as connected to session."""
    code = code.upper()

    # Check if session exists
    session_exists = await redis_client.exists(session_key(code))
    print(f"[REDIS] Speaker connect - session {code} exists: {session_exists}")
    if not session_exists:
        return {"error": "Session not found"}, 404

    # Update speaker connection status
    await redis_client.hset(session_key(code), "speaker_connected", "true")
    print(f"[REDIS] Speaker connect - set speaker_connected to true for session {code}")

    return {"connected": True}

@app.post("/session/speaker/rejoin/{code}")
async def speaker_rejoin(code: str):
    """Allow speaker to rejoin an existing session."""
    code = code.upper()

    # Check if session exists
    session_exists = await redis_client.exists(session_key(code))
    print(f"[REDIS] Speaker rejoin - session {code} exists: {session_exists}")
    if not session_exists:
        return {"error": "Session not found"}, 404

    # Get session info
    session_info = await redis_client.hgetall(session_key(code))
    print(f"[REDIS] Speaker rejoin - session {code} info: {session_info}")

    # Check if session is still active
    if session_info.get("status") != "active":
        return {"error": "Session is not active"}, 400

    # Update speaker connection status
    await redis_client.hset(session_key(code), "speaker_connected", "true")
    print(f"[REDIS] Speaker rejoin - set speaker_connected to true for session {code}")

    # Get existing segments for context
    segments_count = await redis_client.llen(f"{session_key(code)}:segments")
    print(f"[REDIS] Speaker rejoin - session {code} segments count: {segments_count}")

    return {
        "rejoined": True,
        "session": session_info,
        "existing_segments": segments_count
    }

@app.post("/session/end/{code}")
async def end_session(code: str):
    """End a session and mark it as inactive."""
    code = code.upper()

    # Check if session exists
    session_exists = await redis_client.exists(session_key(code))
    print(f"[REDIS] End session - session {code} exists: {session_exists}")
    if not session_exists:
        return {"error": "Session not found"}, 404

    # Update session status to inactive
    end_data = {
        "status": "inactive",
        "ended_at": str(int(asyncio.get_event_loop().time())),
        "speaker_connected": "false"
    }
    await redis_client.hset(session_key(code), mapping=end_data)
    print(f"[REDIS] End session - updated session {code} with data: {end_data}")

    # Notify all audience members that session has ended
    end_message = json.dumps({
        "message": "SessionEnded",
        "timestamp": int(asyncio.get_event_loop().time())
    })
    await redis_client.publish(f"{session_key(code)}:events", end_message)

    # Clean up audience WebSocket tracking
    if code in audience_websockets:
        del audience_websockets[code]

    # Clean up session voice cache
    if code in session_voice_cache:
        del session_voice_cache[code]

    return {
        "ended": True,
        "message": "Session has been ended successfully"
    }

def transform_speechmatics_to_desired_format(sm_response):
    """
    Transform a Speechmatics response to the desired transcript segment format.

    Args:
        sm_response (dict): The response from Speechmatics.

    Returns:
        list: List of transformed transcript segments.
    """
    if sm_response.get("message") != "AddTranscript":
        return None
    results = sm_response.get("results", [])
    transformed_segments = []
    current_segment = None

    for result in results:
        if result.get("type") == "word":
            alternatives = result.get("alternatives", [])
            if alternatives:
                word_data = alternatives[0]
                speaker = word_data.get("speaker", "S1")
                content = word_data.get("content", "")
                start_time = result.get("start_time", 0)
                end_time = result.get("end_time", 0)

                # Aggregate words into a segment
                if current_segment and current_segment["speaker"] == speaker:
                    current_segment["text"] += " " + content
                    current_segment["end_time"] = end_time
                else:
                    if current_segment:
                        transformed_segments.append(current_segment)
                    current_segment = {
                        "text": content,
                        "speaker": speaker,
                        "start_time": start_time,
                        "end_time": end_time,
                    }

        elif result.get("type") == "punctuation":
            alternatives = result.get("alternatives", [])
            if alternatives:
                punctuation_data = alternatives[0]
                speaker = punctuation_data.get("speaker", "S1")
                content = punctuation_data.get("content", "")
                start_time = result.get("start_time", 0)
                end_time = result.get("end_time", 0)
                print(
                    f"🔤 [DEBUG] Punctuation: '{content}' Speaker: {speaker} Start Time: {start_time}, End Time: {end_time}"
                )
                if current_segment and current_segment["speaker"] == speaker:
                    current_segment["text"] += " " + content
                    current_segment["end_time"] = end_time
                else:
                    if current_segment:
                        transformed_segments.append(current_segment)
                    current_segment = {
                        "text": content,
                        "speaker": speaker,
                        "start_time": start_time,
                        "end_time": end_time,
                    }

    if current_segment:
        transformed_segments.append(current_segment)
    return transformed_segments


async def handle_live_mode(websocket, session_id, audio_buffer, all_segments, session_code=None):
    """Handle live transcription mode - existing Speechmatics integration."""
    seq_no = 0
    audio_url = None
    timeout_task = None

    try:
        print(f"[DEBUG] [{session_id}] Opening Speechmatics relay...")
        async with websockets.connect(
            SM_WS_URL,
            additional_headers={"Authorization": f"Bearer {SPEECHMATICS_API_KEY}"},
        ) as sm_ws:

            print(f"[DEBUG] [{session_id}] Connected to Speechmatics.")

            # 1. Send StartRecognition
            start_msg = {
                "message": "StartRecognition",
                "audio_format": {
                    "type": "raw",
                    "encoding": "pcm_s16le",
                    "sample_rate": SAMPLE_RATE,
                },
                "transcription_config": {
                    "language": LANG,
                    "operating_point": "enhanced",
                    "diarization": "speaker",
                    "enable_partials": True,
                    "max_delay": 2.0,
                },
            }
            await sm_ws.send(json.dumps(start_msg))

            # 2. Wait for RecognitionStarted
            while True:
                msg = await sm_ws.recv()
                try:
                    msg_obj = json.loads(msg)
                    if msg_obj.get("message") == "RecognitionStarted":
                        print(
                            f"[DEBUG] [{session_id}] Speechmatics RecognitionStarted."
                        )
                        break
                    else:
                        await safe_websocket_send(websocket, msg, session_code)
                except Exception as e:
                    print(f"[ERROR] [{session_id}] Error parsing initial message: {e}")

            async def wait_for_end_of_transcript():
                try:
                    await asyncio.sleep(30)
                    print(f"[DEBUG] [{session_id}] Timeout waiting for EndOfTranscript, processing anyway")
                except asyncio.CancelledError:
                    # Task was cancelled on normal completion; no problem.
                    print(f"[DEBUG] [{session_id}] Timeout task cancelled cleanly")
                    return


            # --- Main relay coroutines ---
            async def client_to_sm():
                """
                Relay audio data from client to Speechmatics, save/upload the audio buffer after stream ends.
                """
                nonlocal seq_no, audio_url
                try:
                    while True:
                        ws_msg = await websocket.receive()
                        if ws_msg["type"] == "websocket.disconnect":
                            print(
                                f"[DEBUG] [{session_id}] WebSocket disconnect received"
                            )
                            # Mark speaker as disconnected
                            if session_code:
                                await redis_client.hset(session_key(session_code), "speaker_connected", "false")
                            await websocket.close()
                            break
                        elif "bytes" in ws_msg and ws_msg["bytes"]:
                            audio_buffer.extend(ws_msg["bytes"])
                            await sm_ws.send(ws_msg["bytes"])
                            seq_no += 1
                        elif "text" in ws_msg and ws_msg["text"]:
                            if ws_msg["text"] == "END":
                                await sm_ws.send(
                                    json.dumps(
                                        {
                                            "message": "EndOfStream",
                                            "last_seq_no": seq_no,
                                        }
                                    )
                                )
                                break
                except Exception as e:
                    print(f"[ERROR] [{session_id}] Client audio relay error: {e}")

            async def sm_to_client():
                """
                Relay messages from Speechmatics to client, generate summary, and send final messages.

                Returns:
                    None
                """
                nonlocal all_segments, timeout_task, audio_url
                try:
                    async for msg in sm_ws:
                        try:
                            msg_obj = json.loads(msg)
                            message_type = msg_obj.get("message")
                            if message_type == "AddTranscript":
                                segments = transform_speechmatics_to_desired_format(
                                    msg_obj
                                )
                                if segments:
                                    all_segments.extend(segments)
                                partial_response = {
                                    "message": "PartialTranscript",
                                    "segments": segments or [],
                                    "all_segments": all_segments,
                                }
                                await safe_websocket_send(
                                    websocket, json.dumps(partial_response), session_code
                                )
                            elif message_type == "AddPartialTranscript":
                                segments = transform_speechmatics_to_desired_format(
                                    msg_obj
                                )
                                partial_response = {
                                    "message": "AddPartialTranscript",
                                    "segments": segments or [],
                                }
                                await safe_websocket_send(
                                    websocket, json.dumps(partial_response), session_code
                                )
                            else:
                                await safe_websocket_send(websocket, msg, session_code)
                        except json.JSONDecodeError:
                            await safe_websocket_send(websocket, msg, session_code)
                        except Exception as e:
                            print(
                                f"[ERROR] [{session_id}] Error processing Speechmatics message: {e}"
                            )
                except Exception as e:
                    print(f"[ERROR] [{session_id}] Speechmatics relay error: {e}")
                finally:
                    try:
                        await sm_ws.close()
                    except Exception as e:
                        print(f"[ERROR] Error closing Speechmatics relay: {e}")

            timeout_task = asyncio.create_task(wait_for_end_of_transcript())
            await asyncio.gather(client_to_sm(), sm_to_client(), timeout_task)

    except Exception as e:
        print(f"[ERROR] [{session_id}] Live mode error: {e}")
        await safe_websocket_send(websocket, json.dumps({
            "message": "Error",
            "error": str(e)
        }), session_code)

async def safe_websocket_send(websocket, message, session_code=None):
    """
    Safely send a message via WebSocket, store in Redis, and broadcast to audience.

    Args:
        websocket (WebSocket): FastAPI WebSocket object.
        message (str): The message to send.
        session_code (str): Session code for Redis storage and broadcasting.

    Returns:
        bool: True if send succeeds, False otherwise.
    """
    try:
        # Send to speaker WebSocket
        await websocket.send_text(message)

        # If session_code provided, store in Redis and broadcast to audience
        if session_code:
            await store_and_broadcast_message(session_code, message)

        return True
    except Exception as e:
        print(f"🔍 DEBUG: Error sending WebSocket message: {e}")
        return False

async def store_and_broadcast_message(session_code, message):
    """
    Store transcript message in Redis and broadcast to audience members.

    Args:
        session_code (str): Session code
        message (str): Message to store and broadcast
    """
    try:
        # Parse message to check if it's a transcript
        try:
            msg_obj = json.loads(message)
            if msg_obj.get("message") in ["PartialTranscript", "AddPartialTranscript"]:
                # Store transcript segments in Redis
                if "segments" in msg_obj and msg_obj["segments"]:
                    for segment in msg_obj["segments"]:
                        segment_data = json.dumps(segment)
                        await redis_client.lpush(f"{session_key(session_code)}:segments", segment_data)
                        print(f"[REDIS] Stored segment for session {session_code}: {segment}")

                    # Update total segments count
                    total_segments = await redis_client.llen(f"{session_key(session_code)}:segments")
                    await redis_client.hset(session_key(session_code), "total_segments", str(total_segments))
                    print(f"[REDIS] Updated total_segments for session {session_code}: {total_segments}")

                # Generate TTS for translated text if available
                if "segments" in msg_obj and msg_obj["segments"]:
                    for segment in msg_obj["segments"]:
                        translated_text = segment.get("translated", "")
                        if translated_text and ELEVENLABS_API_KEY:
                            try:
                                audio_data = await generate_tts_audio(translated_text, session_code)
                                if audio_data:
                                    segment["audio"] = audio_data
                            except Exception as e:
                                print(f"[ERROR] TTS generation failed: {e}")

                    # Update message with audio data
                    message = json.dumps(msg_obj)

                # Broadcast to audience via Redis pub/sub
                await redis_client.publish(f"{session_key(session_code)}:events", message)

        except json.JSONDecodeError:
            # If not JSON, still broadcast as is
            await redis_client.publish(f"{session_key(session_code)}:events", message)

    except Exception as e:
        print(f"🔍 DEBUG: Error storing/broadcasting message: {e}")

# ElevenLabs TTS integration
async def generate_tts_audio(text: str, session_code: str = None) -> str:
    """Generate TTS audio using ElevenLabs API and return base64 encoded audio."""
    if not ELEVENLABS_API_KEY or not text.strip():
        return None

    # Get or set consistent voice for this session
    voice_id = ELEVENLABS_VOICE_ID
    if session_code:
        if session_code not in session_voice_cache:
            session_voice_cache[session_code] = ELEVENLABS_VOICE_ID
        voice_id = session_voice_cache[session_code]

    try:
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": ELEVENLABS_API_KEY
        }

        # Optimized settings for consistent, clear speech
        data = {
            "text": text,
            "model_id": "eleven_turbo_v2",
            "voice_settings": {
                "stability": 0.7,  # Higher stability for consistency
                "similarity_boost": 0.8,  # Higher similarity for voice consistency
                "style": 0.2,  # Slight style for natural speech
                "use_speaker_boost": True
            },
            "output_format": "mp3_44100_128"  # High quality audio
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=data, headers=headers, timeout=30.0)

            if response.status_code == 200:
                # Encode audio data as base64
                audio_base64 = base64.b64encode(response.content).decode('utf-8')
                print(f"[DEBUG] Generated TTS audio for session {session_code}, length: {len(audio_base64)} chars")
                return audio_base64
            else:
                print(f"[ERROR] ElevenLabs API error: {response.status_code} - {response.text}")
                return None

    except Exception as e:
        print(f"[ERROR] TTS generation error: {e}")
        return None



@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8081, reload=False)
